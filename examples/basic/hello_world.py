#!/usr/bin/env python3
"""
🚀 AI-PyResolver Hello World Example

This is your first AI-PyResolver script! It demonstrates the basic usage
of AI-powered dependency resolution.

Run this example:
    python hello_world.py
"""

from pyresolver import PyResolver
from pyresolver.core.resolver import ResolverConfig
from pyresolver.core.models import ResolutionStrategy


def main():
    """
    Hello World example showing basic AI-PyResolver usage.
    """
    print("🚀 Welcome to AI-PyResolver!")
    print("=" * 50)
    
    # Create a resolver with default AI-optimized configuration
    print("\n📦 Creating AI-powered resolver...")
    resolver = PyResolver()
    
    # Define some common packages that often conflict
    requirements = [
        "requests>=2.25.0",
        "urllib3>=1.26.0",
        "certifi>=2021.5.25"
    ]
    
    print(f"\n🎯 Resolving packages: {requirements}")
    
    # Resolve dependencies using AI
    print("\n🧠 AI is analyzing millions of successful installations...")
    result = resolver.resolve(requirements)
    
    # Display results
    print(f"\n✅ Resolution Status: {'SUCCESS' if result.is_successful else 'FAILED'}")
    print(f"⏱️  Resolution Time: {result.resolution_time:.3f} seconds")
    print(f"📊 Packages Resolved: {result.package_count}")
    print(f"🧠 Strategy Used: {result.strategy.value}")
    
    if result.is_successful and result.resolved_packages:
        print("\n📋 Resolved Package Versions:")
        for name, package_version in result.resolved_packages.items():
            print(f"   • {package_version}")
    
    if result.conflicts:
        print("\n⚠️  Conflicts Found:")
        for conflict in result.conflicts:
            print(f"   • {conflict}")
    
    print("\n🎉 That's it! You've successfully used AI-PyResolver!")
    print("💡 Try modifying the requirements list above to see how AI handles different scenarios.")


if __name__ == "__main__":
    main()
