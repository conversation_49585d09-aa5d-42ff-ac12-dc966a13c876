name: ✨ Feature Request
description: Suggest a new feature for AI-PyResolver
title: "[FEATURE] "
labels: ["enhancement", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thanks for suggesting a new feature! ✨
        
        We love hearing ideas from the community to make AI-PyResolver even better.

  - type: checkboxes
    id: checks
    attributes:
      label: Pre-flight Checklist
      description: Please confirm you have done the following
      options:
        - label: I have searched existing issues to ensure this feature hasn't been requested
          required: true
        - label: I have read the documentation and this feature doesn't exist
          required: true

  - type: textarea
    id: problem
    attributes:
      label: 🎯 Problem Statement
      description: What problem does this feature solve?
      placeholder: |
        Is your feature request related to a problem? Please describe.
        A clear and concise description of what the problem is.
        Ex. I'm always frustrated when [...]
    validations:
      required: true

  - type: textarea
    id: solution
    attributes:
      label: 💡 Proposed Solution
      description: What would you like to happen?
      placeholder: |
        A clear and concise description of what you want to happen.
        Include any specific API changes, new commands, or functionality.
    validations:
      required: true

  - type: textarea
    id: alternatives
    attributes:
      label: 🔄 Alternatives Considered
      description: What alternatives have you considered?
      placeholder: |
        A clear and concise description of any alternative solutions 
        or features you've considered.

  - type: textarea
    id: examples
    attributes:
      label: 📝 Usage Examples
      description: How would this feature be used?
      placeholder: |
        Provide code examples or usage scenarios:
        
        ```python
        from pyresolver import PyResolver
        
        # Example of how the new feature would work
        resolver = PyResolver()
        result = resolver.new_feature_method()
        ```
    validations:
      required: true

  - type: dropdown
    id: priority
    attributes:
      label: 📊 Priority
      description: How important is this feature to you?
      options:
        - Low - Nice to have
        - Medium - Would improve my workflow
        - High - Critical for my use case
    validations:
      required: true

  - type: checkboxes
    id: contribution
    attributes:
      label: 🤝 Contribution
      description: Are you willing to help implement this feature?
      options:
        - label: I'm willing to submit a PR for this feature
        - label: I can help with testing
        - label: I can help with documentation

  - type: textarea
    id: additional
    attributes:
      label: 📝 Additional Context
      description: Any other context or screenshots about the feature request
      placeholder: Add any other context or screenshots about the feature request here.
