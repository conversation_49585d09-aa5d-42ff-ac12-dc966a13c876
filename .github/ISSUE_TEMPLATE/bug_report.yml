name: 🐛 Bug Report
description: Report a bug in AI-PyResolver
title: "[BUG] "
labels: ["bug", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to report a bug! 🐛
        
        Please fill out this form as completely as possible to help us reproduce and fix the issue.

  - type: checkboxes
    id: checks
    attributes:
      label: Pre-flight Checklist
      description: Please confirm you have done the following
      options:
        - label: I have searched existing issues to ensure this bug hasn't been reported
          required: true
        - label: I have read the documentation
          required: true
        - label: I am using the latest version of AI-PyResolver
          required: true

  - type: textarea
    id: description
    attributes:
      label: 🐛 Bug Description
      description: A clear and concise description of what the bug is
      placeholder: Describe the bug...
    validations:
      required: true

  - type: textarea
    id: reproduction
    attributes:
      label: 🔄 Steps to Reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. Install AI-PyResolver with `pip install ai-pyresolver`
        2. Run the following code:
        ```python
        from pyresolver import PyResolver
        # Your code here
        ```
        3. See error
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: ✅ Expected Behavior
      description: What you expected to happen
      placeholder: Describe what should have happened...
    validations:
      required: true

  - type: textarea
    id: actual
    attributes:
      label: ❌ Actual Behavior
      description: What actually happened
      placeholder: Describe what actually happened...
    validations:
      required: true

  - type: textarea
    id: environment
    attributes:
      label: 🌍 Environment
      description: Your environment details
      placeholder: |
        - OS: [e.g. Ubuntu 22.04, Windows 11, macOS 13]
        - Python version: [e.g. 3.11.0]
        - AI-PyResolver version: [e.g. 0.1.0]
        - pip version: [e.g. 23.0.1]
    validations:
      required: true

  - type: textarea
    id: logs
    attributes:
      label: 📋 Error Logs
      description: Any relevant error messages or logs
      placeholder: Paste error logs here...
      render: shell

  - type: textarea
    id: additional
    attributes:
      label: 📝 Additional Context
      description: Any other context about the problem
      placeholder: Add any other context about the problem here...
