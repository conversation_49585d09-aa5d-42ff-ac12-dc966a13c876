name: 🚀 CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

jobs:
  test:
    name: 🧪 Test Suite
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ['3.8', '3.9', '3.10', '3.11', '3.12']
        
    steps:
    - name: 📥 Checkout Code
      uses: actions/checkout@v4
      
    - name: 🐍 Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        
    - name: 📦 Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev]"
        
    - name: 🔍 Lint with flake8
      run: |
        flake8 pyresolver/ --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 pyresolver/ --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
        
    - name: 🎯 Type Check with mypy
      run: mypy pyresolver/
      
    - name: 🧪 Run Tests
      run: |
        pytest --cov=pyresolver --cov-report=xml --cov-report=html
        
    - name: 📊 Upload Coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  security:
    name: 🔒 Security Scan
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: 🛡️ Run Bandit Security Scan
      uses: securecodewarrior/github-action-bandit@v1
      with:
        path: "pyresolver/"
        
  performance:
    name: ⚡ Performance Benchmarks
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    - name: 📦 Install Dependencies
      run: |
        pip install -e ".[dev]"
    - name: 🏃‍♂️ Run Benchmarks
      run: |
        python benchmarks/run_benchmarks.py
        
  build:
    name: 🏗️ Build Package
    runs-on: ubuntu-latest
    needs: [test, security]
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    - name: 🔨 Build Package
      run: |
        pip install build
        python -m build
    - name: 📤 Upload Artifacts
      uses: actions/upload-artifact@v3
      with:
        name: dist
        path: dist/

  publish:
    name: 🚀 Publish to PyPI
    runs-on: ubuntu-latest
    needs: [build]
    if: github.event_name == 'release'
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    - name: 📥 Download Artifacts
      uses: actions/download-artifact@v3
      with:
        name: dist
        path: dist/
    - name: 🚀 Publish to PyPI
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        password: ${{ secrets.PYPI_API_TOKEN }}
