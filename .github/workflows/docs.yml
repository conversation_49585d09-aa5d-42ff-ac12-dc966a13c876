name: 📚 Documentation

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  docs:
    name: 📖 Build Documentation
    runs-on: ubuntu-latest
    steps:
    - name: 📥 Checkout
      uses: actions/checkout@v4
      
    - name: 🐍 Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        
    - name: 📦 Install Dependencies
      run: |
        pip install -e ".[docs]"
        pip install sphinx sphinx-rtd-theme myst-parser
        
    - name: 🏗️ Build Docs
      run: |
        cd docs
        make html
        
    - name: 🚀 Deploy to GitHub Pages
      if: github.ref == 'refs/heads/main'
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./docs/_build/html

  link-check:
    name: 🔗 Check Links
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: 🔍 Check Links
      uses: gaurav-nelson/github-action-markdown-link-check@v1
      with:
        use-quiet-mode: 'yes'
        use-verbose-mode: 'yes'
