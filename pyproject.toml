[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "pyresolver"
version = "0.1.0"
description = "AI-powered Python dependency resolver"
readme = "README.md"
license = "MIT"
requires-python = ">=3.9"
authors = [
    {name = "PyResolver Team", email = "<EMAIL>"},
]
keywords = ["dependency", "resolver", "ai", "ml", "python", "packaging"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: System :: Software Distribution",
]

dependencies = [
    "torch>=2.0.0",
    "numpy>=1.21.0",
    "pandas>=1.3.0",
    "scikit-learn>=1.0.0",
    "requests>=2.25.0",
    "packaging>=21.0",
    "click>=8.0.0",
    "rich>=12.0.0",
    "pydantic>=2.0.0",
    "sqlalchemy>=2.0.0",
    "aiohttp>=3.8.0",
    "networkx>=2.6.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
]
docs = [
    "mkdocs>=1.4.0",
    "mkdocs-material>=9.0.0",
    "mkdocstrings[python]>=0.20.0",
]
api = [
    "fastapi>=0.100.0",
    "uvicorn>=0.20.0",
    "redis>=4.5.0",
]

[project.urls]
Homepage = "https://github.com/pyresolver/pyresolver"
Documentation = "https://pyresolver.readthedocs.io"
Repository = "https://github.com/pyresolver/pyresolver"
Issues = "https://github.com/pyresolver/pyresolver/issues"

[project.scripts]
pyresolver = "pyresolver.cli:main"

[tool.hatch.build.targets.wheel]
packages = ["pyresolver"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--cov=pyresolver",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--strict-markers",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]